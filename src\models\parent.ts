import {
  getStudentQuestionnaires,
  getStudentTeachers,
  getStudentsQuestionnaireStatus,
  submitParentEvaluation,
  verifyParentPhone,
} from '@/services';
import { handleApiResponse, handleException } from '@/utils/errorHandler';
import { message } from 'antd';
import { useCallback, useState } from 'react';

/**
 * 家长端数据模型
 * @description 管理家长端问卷填写流程的状态和数据
 */
export default function useParentModel() {
  // 基础状态
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState<
    'phone' | 'student' | 'questionnaire' | 'evaluation'
  >('phone');

  // 家长信息
  const [parentPhone, setParentPhone] = useState('');
  const [parentInfo, setParentInfo] = useState<any>(null);

  // 学生信息
  const [studentList, setStudentList] = useState<API.ISSoStudentInfo[]>([]);
  const [selectedStudent, setSelectedStudent] =
    useState<API.ISSoStudentInfo | null>(null);
  const [studentQuestionnaireStatus, setStudentQuestionnaireStatus] = useState<
    API.IStudentQuestionnaireStatus[]
  >([]);
  const [schoolCodeFilter, setSchoolCodeFilter] = useState<string>('');

  // 问卷信息
  const [questionnaireList, setQuestionnaireList] = useState<
    API.IParentQuestionnaireInfo[]
  >([]);
  const [selectedQuestionnaire, setSelectedQuestionnaire] =
    useState<API.IParentQuestionnaireInfo | null>(null);
  const [teacherList, setTeacherList] = useState<API.IStudentTeacherInfo[]>([]);

  // 评价数据
  const [evaluationData, setEvaluationData] = useState<API.IEvaluationData>({
    school_rating: 0,
    school_comment: '',
    teacher_evaluations: [],
  });

  // 本地缓存键名
  const getCacheKey = (suffix: string) => {
    return `parent_evaluation_${parentPhone}_${selectedStudent?.id}_${suffix}`;
  };

  // 获取学生问卷状态
  const fetchStudentsQuestionnaireStatus = useCallback(
    async (studentIds: string[], schoolCode?: string) => {
      if (!parentPhone || studentIds.length === 0) return;

      setLoading(true);
      try {
        const response = await getStudentsQuestionnaireStatus({
          student_ids: studentIds,
          parent_phone: parentPhone,
          school_code: schoolCode || schoolCodeFilter,
        });

        const result = handleApiResponse(response);
        if (result.success) {
          setStudentQuestionnaireStatus(result.data || []);
        } else {
          setStudentQuestionnaireStatus([]);
        }
      } catch (error) {
        handleException(error, '获取问卷状态失败');
        setStudentQuestionnaireStatus([]);
      } finally {
        setLoading(false);
      }
    },
    [parentPhone, schoolCodeFilter],
  );

  // 设置学校筛选
  const setSchoolFilter = useCallback(
    async (schoolCode: string) => {
      setSchoolCodeFilter(schoolCode);
      if (studentList.length > 0) {
        await fetchStudentsQuestionnaireStatus(
          studentList.map(s => s.id),
          schoolCode,
        );
      }
    },
    [studentList, fetchStudentsQuestionnaireStatus],
  );

  // 验证家长手机号
  const verifyPhone = useCallback(async (phone: string) => {
    setLoading(true);
    try {
      const response = await verifyParentPhone({ phone });
      const result = handleApiResponse(
        response,
        '手机号验证成功',
        '手机号验证失败，请检查是否已关联学生',
      );
      console.log(result);

      if (result.success && result.data?.is_valid) {
        const { parent } = response.data!;
        const { children, ...parentInfo } = parent!;
        setParentPhone(phone);
        setParentInfo(parentInfo);
        const students = children.map((c) => ({
          /** 学生ID */
          id: c.student.id,
          /** 学生姓名 */
          name: c.student.name,
          /** 学生班级 */
          class: c.student.class.name,
          /** 学生年级 */
          grade: c.student.class.grade_name,
          /** 学生状态（active/inactive） */
          status: c.student.status,
          /** 所属学校代码 */
          school_code: c.student.class.enterprise.code,
        }));

        setStudentList(students);

        // 获取所有学生的问卷状态
        await fetchStudentsQuestionnaireStatus(students.map(s => s.id));

        setCurrentStep('student');
        return true;
      } else {
        return false;
      }
    } catch (error) {
      handleException(error, '验证失败，请稍后重试');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // 选择问卷
  const selectQuestionnaire = useCallback(
    async (questionnaire: API.IParentQuestionnaireInfo) => {
      setLoading(true);
      try {
        setSelectedQuestionnaire(questionnaire);

        // 检查是否已提交
        if (questionnaire.is_submitted) {
          message.warning('该问卷已提交，无法重复填写');
          return false;
        }

        // 获取教师列表
        const teachersResponse = await getStudentTeachers({
          student_id: selectedStudent!.id,
          questionnaire_id: questionnaire.questionnaire_id,
        });

        const teachersResult = handleApiResponse(teachersResponse);

        if (teachersResult.success) {
          setTeacherList(teachersResult.data || []);

          // 尝试从本地缓存恢复数据
          const cachedData = localStorage.getItem(getCacheKey('evaluation'));
          if (cachedData) {
            try {
              const parsed = JSON.parse(cachedData);
              setEvaluationData(parsed);
              message.info('已恢复之前填写的内容');
            } catch (e) {
              console.warn('缓存数据解析失败:', e);
            }
          }

          setCurrentStep('evaluation');
          return true;
        } else {
          message.error(teachersResult.error);
          return false;
        }
      } catch (error) {
        handleException(error, '操作失败，请稍后重试');
        return false;
      } finally {
        setLoading(false);
      }
    },
    [selectedStudent, parentPhone],
  );

  // 选择学生
  const selectStudent = useCallback(
    async (student: API.ISSoStudentInfo) => {
      setLoading(true);
      try {
        setSelectedStudent(student);

        // 获取学生的所有问卷列表
        const questionnairesResponse = await getStudentQuestionnaires({
          student_id: student.id,
          parent_phone: parentPhone,
        });

        const questionnairesResult = handleApiResponse(questionnairesResponse);

        if (questionnairesResult.success && questionnairesResult.data) {
          setQuestionnaireList(questionnairesResult.data.questionnaires || []);

          // 如果只有一个问卷，直接跳转到评价页面
          if (questionnairesResult.data.questionnaires.length === 1) {
            const questionnaire = questionnairesResult.data.questionnaires[0];
            return await selectQuestionnaire(questionnaire);
          } else {
            // 多个问卷，跳转到问卷选择页面
            setCurrentStep('questionnaire');
            return true;
          }
        } else {
          message.error(questionnairesResult.error || '获取问卷列表失败');
          return false;
        }
      } catch (error) {
        handleException(error, '操作失败，请稍后重试');
        return false;
      } finally {
        setLoading(false);
      }
    },
    [parentPhone],
  );

  // 更新评价数据
  const updateEvaluationData = useCallback(
    (data: Partial<API.IEvaluationData>) => {
      const newData = { ...evaluationData, ...data };
      setEvaluationData(newData);

      // 保存到本地缓存
      if (selectedStudent) {
        localStorage.setItem(
          getCacheKey('evaluation'),
          JSON.stringify(newData),
        );
      }
    },
    [evaluationData, selectedStudent, parentPhone],
  );

  // 更新学校评分
  const updateSchoolRating = useCallback(
    (rating: number, comment?: string) => {
      updateEvaluationData({
        school_rating: rating,
        school_comment: comment || evaluationData.school_comment,
      });
    },
    [updateEvaluationData, evaluationData.school_comment],
  );

  // 更新教师评分
  const updateTeacherRating = useCallback(
    (teacherId: string, rating: number, comment?: string) => {
      const existingIndex = evaluationData.teacher_evaluations.findIndex(
        (evaluation) => evaluation.teacher_id === teacherId,
      );

      const newEvaluation = {
        teacher_id: teacherId,
        rating,
        comment: comment || '',
      };
      let newTeacherEvaluations;

      if (existingIndex >= 0) {
        newTeacherEvaluations = [...evaluationData.teacher_evaluations];
        newTeacherEvaluations[existingIndex] = newEvaluation;
      } else {
        newTeacherEvaluations = [
          ...evaluationData.teacher_evaluations,
          newEvaluation,
        ];
      }

      updateEvaluationData({
        teacher_evaluations: newTeacherEvaluations,
      });
    },
    [evaluationData.teacher_evaluations, updateEvaluationData],
  );

  // 检查是否所有教师都已评分
  const isAllTeachersRated = useCallback(() => {
    const unratedTeachers = teacherList.filter(
      (teacher) =>
        !teacher.is_evaluated &&
        !evaluationData.teacher_evaluations.some(
          (evaluation) => evaluation.teacher_id === teacher.teacher_id,
        ),
    );
    return unratedTeachers.length === 0 && evaluationData.school_rating > 0;
  }, [teacherList, evaluationData]);

  // 重置状态
  const resetState = useCallback(() => {
    setCurrentStep('phone');
    setParentPhone('');
    setParentInfo(null);
    setStudentList([]);
    setSelectedStudent(null);
    setQuestionnaireList([]);
    setSelectedQuestionnaire(null);
    setTeacherList([]);
    setStudentQuestionnaireStatus([]);
    setSchoolCodeFilter('');
    setEvaluationData({
      school_rating: 0,
      school_comment: '',
      teacher_evaluations: [],
    });
  }, []);

  // 提交评价
  const submitEvaluation = useCallback(async () => {
    if (!selectedStudent || !selectedQuestionnaire || !isAllTeachersRated()) {
      message.warning('请完成所有评分后再提交');
      return false;
    }

    setLoading(true);
    try {
      const response = await submitParentEvaluation({
        questionnaire_id: selectedQuestionnaire.questionnaire_id,
        student_id: selectedStudent.id,
        parent_phone: parentPhone,
        evaluation_data: evaluationData,
      });

      const result = handleApiResponse(
        response,
        '提交成功！感谢您的评价',
        '提交失败，请稍后重试',
      );

      if (result.success) {
        // 清除本地缓存
        localStorage.removeItem(getCacheKey('evaluation'));

        // 重置状态
        resetState();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      handleException(error, '提交失败，请稍后重试');
      return false;
    } finally {
      setLoading(false);
    }
  }, [
    selectedStudent,
    selectedQuestionnaire,
    parentPhone,
    evaluationData,
    isAllTeachersRated,
  ]);

  // 返回上一步
  const goBack = useCallback(() => {
    if (currentStep === 'evaluation') {
      setCurrentStep('questionnaire');
      setSelectedQuestionnaire(null);
      setTeacherList([]);
    } else if (currentStep === 'questionnaire') {
      setCurrentStep('student');
      setQuestionnaireList([]);
      setSelectedStudent(null);
    } else if (currentStep === 'student') {
      setCurrentStep('phone');
      setStudentList([]);
      setParentInfo(null);
    }
  }, [currentStep]);

  return {
    // 状态
    loading,
    currentStep,
    parentPhone,
    parentInfo,
    studentList,
    selectedStudent,
    questionnaireList,
    selectedQuestionnaire,
    teacherList,
    evaluationData,
    studentQuestionnaireStatus,
    schoolCodeFilter,

    // 计算属性
    isAllTeachersRated: isAllTeachersRated(),

    // 方法
    verifyPhone,
    selectStudent,
    selectQuestionnaire,
    updateSchoolRating,
    updateTeacherRating,
    submitEvaluation,
    resetState,
    goBack,
    fetchStudentsQuestionnaireStatus,
    setSchoolFilter,
  };
}
