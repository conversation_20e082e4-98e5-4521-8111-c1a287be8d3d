import { Line, Pie } from '@ant-design/charts';
import {
  ExclamationCircleOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { Card, Col, Row, Skeleton, Statistic, Table, Typography } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

interface LogStatisticsProps {
  data: API.IOperationLogStatistics | null;
  loading?: boolean;
}

/**
 * 日志统计组件
 */
const LogStatistics: React.FC<LogStatisticsProps> = ({
  data,
  loading = false,
}) => {
  // 趋势图配置
  const trendConfig = React.useMemo(() => {
    if (!data?.operation_trend || !Array.isArray(data.operation_trend)) return null;

    const chartData: any[] = [];
    // 根据实际接口返回的数据结构适配
    data.operation_trend.forEach((item: any) => {
      const date = item.time_period || item.date || item.time;
      const totalCount = item.total_count || item.count || item.total || 0;
      const successCount = item.success_count || 0;
      const failedCount = item.failed_count || 0;

      // 添加总操作数
      chartData.push({
        date: date,
        value: totalCount,
        type: '总操作数',
      });

      // 添加成功操作数
      chartData.push({
        date: date,
        value: successCount,
        type: '成功操作',
      });

      // 添加失败操作数
      chartData.push({
        date: date,
        value: failedCount,
        type: '失败操作',
      });
    });

    return {
      data: chartData,
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000,
        },
      },
      color: ['#1890ff', '#52c41a', '#ff4d4f'],
      point: {
        size: 4,
        shape: 'circle',
      },
      tooltip: {
        formatter: (datum: any) => {
          return {
            name: datum.type,
            value: `${datum.value} 条`,
          };
        },
      },
      legend: {
        position: 'top' as const,
      },
      yAxis: {
        title: {
          text: '操作数量',
        },
        min: 0,
      },
      xAxis: {
        title: {
          text: '日期',
        },
        type: 'time',
      },
    };
  }, [data?.operation_trend]);

  // 饼图配置
  const pieConfig = React.useMemo(() => {
    if (!data?.module_distribution || !Array.isArray(data.module_distribution)) return null;

    // 获取模块名称映射
    const getModuleName = (module: string) => {
      const moduleMap: Record<string, string> = {
        questionnaire: '问卷管理',
        response: '评价提交',
        statistics: '统计查询',
        user: '用户管理',
        auth: '用户认证',
        log: '日志管理',
      };
      return moduleMap[module] || module;
    };

    const chartData = data.module_distribution.map((item: any) => {
      const moduleName = getModuleName(item.module || item.type);
      const percentage = item.percentage || ((item.count / data.total_operations) * 100);

      return {
        type: moduleName,
        value: item.count,
        percentage: typeof percentage === 'number' ? percentage.toFixed(1) : percentage,
      };
    });

    return {
      data: chartData,
      angleField: 'value',
      colorField: 'type',
      radius: 0.8,
      label: {
        type: 'outer',
        content: '{name} {percentage}%',
      },
      tooltip: {
        formatter: (datum: any) => {
          return {
            name: datum.type,
            value: `${datum.value} 条 (${datum.percentage}%)`,
          };
        },
      },
      interactions: [
        {
          type: 'element-active',
        },
      ],
      legend: {
        position: 'bottom' as const,
      },
    };
  }, [data?.module_distribution, data?.total_operations]);

  if (loading) {
    return (
      <>
        {/* 统计卡片骨架 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          {[1, 2, 3, 4].map((item) => (
            <Col span={6} key={item}>
              <Card>
                <Skeleton active paragraph={{ rows: 2 }} />
              </Card>
            </Col>
          ))}
        </Row>

        {/* 图表骨架 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Card title="近7天日志趋势">
              <Skeleton active paragraph={{ rows: 8 }} />
            </Card>
          </Col>
          <Col span={8}>
            <Card title="操作类型分布">
              <Skeleton active paragraph={{ rows: 8 }} />
            </Card>
          </Col>
        </Row>
      </>
    );
  }

  if (!data) {
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Card>
            <div
              style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}
            >
              暂无统计数据
            </div>
          </Card>
        </Col>
      </Row>
    );
  }

  return (
    <>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总操作数"
              value={data.total_operations}
              prefix={<FileTextOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff', fontSize: '24px' }}
              formatter={(value) => (
                <span
                  style={{
                    display: 'inline-flex',
                    alignItems: 'baseline',
                    gap: '4px',
                  }}
                >
                  <span>{value}</span>
                  <span style={{ fontSize: '14px', color: '#666' }}>条</span>
                </span>
              )}
            />
          </Card>
        </Col>

        <Col span={6}>
          <Card>
            <Statistic
              title="成功操作"
              value={data.success_operations || 0}
              prefix={<InfoCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontSize: '24px' }}
              formatter={(value) => (
                <span
                  style={{
                    display: 'inline-flex',
                    alignItems: 'baseline',
                    gap: '4px',
                  }}
                >
                  <span>{value}</span>
                  <span style={{ fontSize: '14px', color: '#666' }}>条</span>
                </span>
              )}
            />
          </Card>
        </Col>

        <Col span={6}>
          <Card>
            <Statistic
              title="平均响应时间"
              value={data.avg_response_time || 0}
              prefix={<WarningOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14', fontSize: '24px' }}
              formatter={(value) => (
                <span
                  style={{
                    display: 'inline-flex',
                    alignItems: 'baseline',
                    gap: '4px',
                  }}
                >
                  <span>{value}</span>
                  <span style={{ fontSize: '14px', color: '#666' }}>ms</span>
                </span>
              )}
            />
          </Card>
        </Col>

        <Col span={6}>
          <Card>
            <Statistic
              title="成功率"
              value={data.success_rate || 0}
              prefix={
                <ExclamationCircleOutlined style={{ color: '#722ed1' }} />
              }
              valueStyle={{ color: '#722ed1', fontSize: '24px' }}
              formatter={(value) => (
                <span
                  style={{
                    display: 'inline-flex',
                    alignItems: 'baseline',
                    gap: '4px',
                  }}
                >
                  <span>{value}</span>
                  <span style={{ fontSize: '14px', color: '#666' }}>%</span>
                </span>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={16}>
          <Card title="操作趋势">
            {trendConfig ? (
              <div style={{ height: 300 }}>
                <Line {...trendConfig} />
              </div>
            ) : (
              <div
                style={{
                  textAlign: 'center',
                  padding: '40px 0',
                  color: '#999',
                }}
              >
                暂无趋势数据
              </div>
            )}
          </Card>
        </Col>

        <Col span={8}>
          <Card title="模块分布">
            {pieConfig ? (
              <div style={{ height: 300 }}>
                <Pie {...pieConfig} />
              </div>
            ) : (
              <div
                style={{
                  textAlign: 'center',
                  padding: '40px 0',
                  color: '#999',
                }}
              >
                暂无分布数据
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 用户活跃度表格 */}
      {data?.user_activity && Array.isArray(data.user_activity) && data.user_activity.length > 0 && (
        <Row gutter={16}>
          <Col span={24}>
            <Card title="用户活跃度">
              <Table
                dataSource={data.user_activity}
                rowKey="operator_user_id"
                pagination={false}
                size="small"
                columns={[
                  {
                    title: '用户ID',
                    dataIndex: 'operator_user_id',
                    key: 'operator_user_id',
                    width: 120,
                  },
                  {
                    title: '用户名称',
                    dataIndex: 'operator_user_name',
                    key: 'operator_user_name',
                    width: 120,
                  },
                  {
                    title: '总操作数',
                    dataIndex: 'operation_count',
                    key: 'operation_count',
                    width: 100,
                    align: 'center',
                    render: (value: number) => (
                      <Typography.Text strong style={{ color: '#1890ff' }}>
                        {value}
                      </Typography.Text>
                    ),
                  },
                  {
                    title: '成功操作',
                    dataIndex: 'success_count',
                    key: 'success_count',
                    width: 100,
                    align: 'center',
                    render: (value: number) => (
                      <Typography.Text style={{ color: '#52c41a' }}>
                        {value}
                      </Typography.Text>
                    ),
                  },
                  {
                    title: '失败操作',
                    dataIndex: 'failed_count',
                    key: 'failed_count',
                    width: 100,
                    align: 'center',
                    render: (value: number) => (
                      <Typography.Text style={{ color: value > 0 ? '#ff4d4f' : '#666' }}>
                        {value}
                      </Typography.Text>
                    ),
                  },
                  {
                    title: '成功率',
                    key: 'success_rate',
                    width: 100,
                    align: 'center',
                    render: (_, record: any) => {
                      const rate = record.operation_count > 0
                        ? ((record.success_count / record.operation_count) * 100).toFixed(1)
                        : '0.0';
                      return (
                        <Typography.Text style={{
                          color: parseFloat(rate) >= 90 ? '#52c41a' : parseFloat(rate) >= 70 ? '#faad14' : '#ff4d4f'
                        }}>
                          {rate}%
                        </Typography.Text>
                      );
                    },
                  },
                  {
                    title: '最后操作时间',
                    dataIndex: 'last_operation_time',
                    key: 'last_operation_time',
                    width: 160,
                    render: (value: string) => (
                      <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                        {dayjs(value).format('YYYY-MM-DD HH:mm:ss')}
                      </Typography.Text>
                    ),
                  },
                ]}
              />
            </Card>
          </Col>
        </Row>
      )}
    </>
  );
};

export default LogStatistics;
