import { StarRating } from '@/components';
import {
  ArrowLeftOutlined,
  CheckOutlined,
  SendOutlined,
  StarOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Alert,
  Button,
  Card,
  Divider,
  Input,
  Modal,
  Progress,
  Space,
  Tag,
  Typography,
} from 'antd';
import React, { useState } from 'react';
import './index.less';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

/**
 * 问卷填写页面
 */
const EvaluationForm: React.FC = () => {
  const {
    loading,
    selectedStudent,
    selectedQuestionnaire,
    teacherList,
    evaluationData,
    isAllTeachersRated,
    updateSchoolRating,
    updateTeacherRating,
    submitEvaluation,
    goBack,
  } = useModel('parent');

  const [showSubmitModal, setShowSubmitModal] = useState(false);

  // 计算评价进度
  const getProgress = () => {
    const totalItems = teacherList.filter((t) => !t.is_evaluated).length + 1; // +1 for school rating
    const completedItems =
      evaluationData.teacher_evaluations.length +
      (evaluationData.school_rating > 0 ? 1 : 0);
    return Math.round((completedItems / totalItems) * 100);
  };

  // 获取教师评分
  const getTeacherRating = (teacherId: string) => {
    const evaluation = evaluationData.teacher_evaluations.find(
      (e) => e.teacher_id === teacherId,
    );
    return evaluation?.rating || 0;
  };

  // 获取教师评价
  const getTeacherComment = (teacherId: string) => {
    const evaluation = evaluationData.teacher_evaluations.find(
      (e) => e.teacher_id === teacherId,
    );
    return evaluation?.comment || '';
  };

  // 处理学校评分
  const handleSchoolRating = (rating: number) => {
    updateSchoolRating(rating, evaluationData.school_comment);
  };

  // 处理学校评价
  const handleSchoolComment = (comment: string) => {
    updateSchoolRating(evaluationData.school_rating, comment);
  };

  // 处理教师评分
  const handleTeacherRating = (teacherId: string, rating: number) => {
    updateTeacherRating(teacherId, rating, getTeacherComment(teacherId));
  };

  // 处理教师评价
  const handleTeacherComment = (teacherId: string, comment: string) => {
    updateTeacherRating(teacherId, getTeacherRating(teacherId), comment);
  };

  // 提交评价
  const handleSubmit = async () => {
    const success = await submitEvaluation();
    if (success) {
      setShowSubmitModal(false);
    }
  };

  if (!selectedStudent || !selectedQuestionnaire) {
    return null;
  }

  const starMode = selectedQuestionnaire.star_mode || 5;
  const availableTeachers = teacherList.filter((t) => !t.is_evaluated);

  return (
    <div className="evaluation-form-container">
      <div className="evaluation-form-content">
        <Card className="form-card">
          {/* 头部信息 */}
          <div className="form-header">
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={goBack}
              className="back-button"
            >
              返回
            </Button>

            <div className="header-info">
              <Title level={3}>{selectedQuestionnaire.title}</Title>
              <Space>
                <Text type="secondary">学生：{selectedStudent.name}</Text>
                <Text type="secondary">班级：{selectedQuestionnaire.class_info.name}</Text>
                <Text type="secondary">
                  学校：{selectedQuestionnaire.school_info.name}
                </Text>
              </Space>
            </div>

            <div className="progress-info">
              <Progress
                percent={getProgress()}
                strokeColor="#1890ff"
                showInfo={false}
              />
              <Text type="secondary">评价进度 {getProgress()}%</Text>
            </div>
          </div>

          <Divider />

          {/* 学校评分区域 */}
          <div className="school-rating-section">
            <Card className="rating-card">
              <div className="rating-header">
                <StarOutlined className="section-icon" />
                <Title level={4}>学校整体评价</Title>
              </div>

              <div className="rating-content">
                <div className="rating-input">
                  <Text strong>请为学校整体表现评分：</Text>
                  <StarRating
                    mode={starMode as 5 | 10}
                    value={evaluationData.school_rating}
                    onChange={handleSchoolRating}
                    size="large"
                    showValue={true}
                  />
                </div>

                <div className="comment-input">
                  <Text strong>评价建议（可选）：</Text>
                  <TextArea
                    placeholder="请输入您对学校的建议或评价..."
                    value={evaluationData.school_comment}
                    onChange={(e) => handleSchoolComment(e.target.value)}
                    rows={3}
                    maxLength={200}
                    showCount
                  />
                </div>
              </div>
            </Card>
          </div>

          {/* 教师评价区域 */}
          <div className="teacher-rating-section">
            <div className="section-header">
              <UserOutlined className="section-icon" />
              <Title level={4}>教师评价</Title>
              <Text type="secondary">
                ({availableTeachers.length} 位教师待评价)
              </Text>
            </div>

            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {availableTeachers.map((teacher) => {
                const rating = getTeacherRating(teacher.teacher_id);
                const comment = getTeacherComment(teacher.teacher_id);
                const isRated = rating > 0;

                return (
                  <Card
                    key={teacher.teacher_id}
                    className={`teacher-card ${isRated ? 'rated' : ''}`}
                  >
                    <div className="teacher-info">
                      <div className="teacher-basic">
                        <Title level={5}>{teacher.teacher_name}</Title>
                        <Space>
                          <Tag color="blue">{teacher.subject}</Tag>
                          {teacher.department && (
                            <Tag color="green">{teacher.department}</Tag>
                          )}
                          {isRated && (
                            <Tag color="success" icon={<CheckOutlined />}>
                              已评价
                            </Tag>
                          )}
                        </Space>
                      </div>

                      <div className="teacher-rating">
                        <div className="rating-input">
                          <Text strong>评分：</Text>
                          <StarRating
                            mode={starMode as 5 | 10}
                            value={rating}
                            onChange={(value) =>
                              handleTeacherRating(teacher.teacher_id, value)
                            }
                            size="default"
                            showValue={true}
                          />
                        </div>

                        <div className="comment-input">
                          <Text strong>评价（可选）：</Text>
                          <TextArea
                            placeholder={`请输入对${teacher.teacher_name}老师的评价...`}
                            value={comment}
                            onChange={(e) =>
                              handleTeacherComment(
                                teacher.teacher_id,
                                e.target.value,
                              )
                            }
                            rows={2}
                            maxLength={100}
                            showCount
                          />
                        </div>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </Space>
          </div>

          {/* 提交按钮 */}
          <div className="submit-section">
            <Button
              type="primary"
              size="large"
              icon={<SendOutlined />}
              onClick={() => setShowSubmitModal(true)}
              disabled={!isAllTeachersRated}
              loading={loading}
              className="submit-button"
            >
              提交评价
            </Button>

            {!isAllTeachersRated && (
              <Alert
                message="请完成所有评分后再提交"
                type="warning"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
          </div>
        </Card>
      </div>

      {/* 提交确认弹窗 */}
      <Modal
        title="确认提交评价"
        open={showSubmitModal}
        onOk={handleSubmit}
        onCancel={() => setShowSubmitModal(false)}
        confirmLoading={loading}
        okText="确认提交"
        cancelText="取消"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            message="提交后将无法修改"
            description="请确认您的评价内容无误后再提交"
            type="warning"
            showIcon
          />

          <div>
            <Paragraph>
              <Text strong>学校评分：</Text>
              <StarRating
                mode={starMode as 5 | 10}
                value={evaluationData.school_rating}
                readOnly={true}
                size="default"
                showValue={true}
              />
            </Paragraph>

            <Paragraph>
              <Text strong>教师评价：</Text>
              {evaluationData.teacher_evaluations.length} 位教师
            </Paragraph>
          </div>
        </Space>
      </Modal>
    </div>
  );
};

export default EvaluationForm;
