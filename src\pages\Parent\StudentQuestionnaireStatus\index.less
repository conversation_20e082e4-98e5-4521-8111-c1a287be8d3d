.student-questionnaire-status-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;

  .student-questionnaire-status-content {
    max-width: 1200px;
    margin: 0 auto;

    .student-status-card {
      height: 100%;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 20px;

        .ant-card-head-title {
          padding: 0;
        }
      }

      .ant-card-body {
        padding: 16px 20px;
      }

      .questionnaire-list {
        max-height: 300px;
        overflow-y: auto;

        .questionnaire-item {
          border-radius: 8px;
          transition: all 0.2s ease;

          &.available {
            border-color: #1890ff;
            cursor: pointer;

            &:hover {
              border-color: #40a9ff;
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
            }
          }

          &.disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }

          .ant-card-body {
            padding: 12px;
          }

          .questionnaire-header {
            margin-bottom: 8px;

            .questionnaire-title {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 4px;
            }

            .questionnaire-meta {
              margin-bottom: 4px;
            }

            .questionnaire-submitted {
              margin-bottom: 4px;
            }
          }

          .questionnaire-action {
            text-align: right;
          }
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .student-questionnaire-status-container {
    padding: 10px;

    .student-questionnaire-status-content {
      .student-status-card {
        .ant-card-head {
          padding: 12px 16px;
        }

        .ant-card-body {
          padding: 12px 16px;
        }

        .questionnaire-list {
          .questionnaire-item {
            .ant-card-body {
              padding: 8px;
            }

            .questionnaire-header {
              .questionnaire-title {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
              }
            }
          }
        }
      }
    }
  }
}

// 滚动条样式
.questionnaire-list {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
