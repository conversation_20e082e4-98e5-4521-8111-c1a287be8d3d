import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  FormOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Avatar,
  Badge,
  Button,
  Card,
  Col,
  Divider,
  Row,
  Space,
  Tag,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import './index.less';

const { Title, Text } = Typography;

/**
 * 学生问卷状态组件
 */
const StudentQuestionnaireStatus: React.FC = () => {
  const {
    loading,
    studentQuestionnaireStatus,
    selectStudent,
    selectQuestionnaire,
  } = useModel('parent');

  // 获取年级颜色
  const getGradeColor = (grade: string) => {
    const gradeColors: Record<string, string> = {
      一年级: 'red',
      二年级: 'orange',
      三年级: 'gold',
      四年级: 'green',
      五年级: 'blue',
      六年级: 'purple',
    };
    return gradeColors[grade] || 'default';
  };

  // 获取问卷状态标签
  const getQuestionnaireStatusTag = (questionnaire: any) => {
    if (questionnaire.is_submitted) {
      return (
        <Tag color="success" icon={<CheckCircleOutlined />}>
          已提交
        </Tag>
      );
    }

    if (!questionnaire.is_available) {
      return (
        <Tag color="default" icon={<ClockCircleOutlined />}>
          不可填写
        </Tag>
      );
    }

    return (
      <Tag color="processing" icon={<ClockCircleOutlined />}>
        待填写
      </Tag>
    );
  };

  // 处理问卷点击
  const handleQuestionnaireClick = async (
    studentStatus: API.IStudentQuestionnaireStatus,
    questionnaire: any,
  ) => {
    // 先选择学生
    const student: API.ISSoStudentInfo = {
      id: studentStatus.student_id,
      name: studentStatus.student_name,
      class: studentStatus.student_class,
      grade: studentStatus.student_grade,
      status: 'active',
      school_code: studentStatus.school_code,
    };

    await selectStudent(student);

    // 再选择问卷
    const questionnaireInfo: API.IParentQuestionnaireInfo = {
      questionnaire_id: questionnaire.questionnaire_id,
      title: questionnaire.title,
      description: questionnaire.description,
      month: questionnaire.month,
      school_info: {
        id: studentStatus.school_code,
        name: studentStatus.school_name,
      },
      class_info: {
        name: studentStatus.student_class,
        grade: studentStatus.student_grade,
      },
      star_mode: questionnaire.star_mode,
      status: questionnaire.status,
      is_submitted: questionnaire.is_submitted,
      submitted_at: questionnaire.submitted_at,
      start_time: questionnaire.start_time,
      end_time: questionnaire.end_time,
    };

    await selectQuestionnaire(questionnaireInfo);
  };

  // 统计问卷状态
  const getQuestionnaireStats = (questionnaires: any[]) => {
    const total = questionnaires.length;
    const submitted = questionnaires.filter(q => q.is_submitted).length;
    const available = questionnaires.filter(q => q.is_available && !q.is_submitted).length;
    
    return { total, submitted, available };
  };

  return (
    <div className="student-questionnaire-status-container">
      <div className="student-questionnaire-status-content">
        <Title level={3} style={{ marginBottom: 24 }}>
          学生问卷状态
        </Title>

        {studentQuestionnaireStatus.length === 0 ? (
          <Card>
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Text type="secondary">暂无学生问卷数据</Text>
            </div>
          </Card>
        ) : (
          <Row gutter={[16, 16]}>
            {studentQuestionnaireStatus.map((studentStatus) => {
              const stats = getQuestionnaireStats(studentStatus.questionnaires);
              
              return (
                <Col xs={24} sm={12} lg={8} key={studentStatus.student_id}>
                  <Card
                    className="student-status-card"
                    title={
                      <Space>
                        <Avatar size={40} icon={<UserOutlined />} />
                        <div>
                          <div style={{ fontWeight: 'bold' }}>
                            {studentStatus.student_name}
                          </div>
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            {studentStatus.student_class}
                          </div>
                        </div>
                      </Space>
                    }
                    extra={
                      <Tag color={getGradeColor(studentStatus.student_grade)}>
                        {studentStatus.student_grade}
                      </Tag>
                    }
                  >
                    {/* 学校信息 */}
                    <div style={{ marginBottom: 16 }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        学校：{studentStatus.school_name}
                      </Text>
                    </div>

                    {/* 问卷统计 */}
                    <div style={{ marginBottom: 16 }}>
                      <Space split={<Divider type="vertical" />}>
                        <Badge count={stats.total} color="blue">
                          <Text style={{ fontSize: '12px' }}>总问卷</Text>
                        </Badge>
                        <Badge count={stats.submitted} color="green">
                          <Text style={{ fontSize: '12px' }}>已提交</Text>
                        </Badge>
                        <Badge count={stats.available} color="orange">
                          <Text style={{ fontSize: '12px' }}>待填写</Text>
                        </Badge>
                      </Space>
                    </div>

                    {/* 问卷列表 */}
                    <div className="questionnaire-list">
                      {studentStatus.questionnaires.map((questionnaire) => (
                        <Card
                          key={questionnaire.questionnaire_id}
                          size="small"
                          className={`questionnaire-item ${
                            questionnaire.is_available && !questionnaire.is_submitted
                              ? 'available'
                              : 'disabled'
                          }`}
                          style={{ marginBottom: 8 }}
                        >
                          <div className="questionnaire-header">
                            <div className="questionnaire-title">
                              <Text strong style={{ fontSize: '13px' }}>
                                {questionnaire.title}
                              </Text>
                              {getQuestionnaireStatusTag(questionnaire)}
                            </div>
                            
                            <div className="questionnaire-meta">
                              <Text type="secondary" style={{ fontSize: '11px' }}>
                                {dayjs(questionnaire.month).format('YYYY年MM月')} | 
                                {questionnaire.star_mode}星制
                              </Text>
                            </div>

                            {questionnaire.submitted_at && (
                              <div className="questionnaire-submitted">
                                <Text type="secondary" style={{ fontSize: '11px' }}>
                                  提交时间：{dayjs(questionnaire.submitted_at).format('MM-DD HH:mm')}
                                </Text>
                              </div>
                            )}
                          </div>

                          <div className="questionnaire-action">
                            <Button
                              type={questionnaire.is_submitted ? 'default' : 'primary'}
                              size="small"
                              icon={questionnaire.is_submitted ? <EyeOutlined /> : <FormOutlined />}
                              loading={loading}
                              disabled={!questionnaire.is_available && !questionnaire.is_submitted}
                              onClick={() => handleQuestionnaireClick(studentStatus, questionnaire)}
                            >
                              {questionnaire.is_submitted ? '查看' : '填写'}
                            </Button>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </Card>
                </Col>
              );
            })}
          </Row>
        )}
      </div>
    </div>
  );
};

export default StudentQuestionnaireStatus;
