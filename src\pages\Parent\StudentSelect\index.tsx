import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  FilterOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Alert,
  Avatar,
  Button,
  Card,
  Select,
  Space,
  Tag,
  Typography,
  Tabs,
} from 'antd';
import React, { useMemo } from 'react';
import StudentQuestionnaireStatus from '../StudentQuestionnaireStatus';
import './index.less';

const { Title, Text } = Typography;

/**
 * 学生选择页面
 */
const StudentSelect: React.FC = () => {
  const {
    loading,
    parentInfo,
    studentList,
    studentQuestionnaireStatus,
    schoolCodeFilter,
    selectStudent,
    setSchoolFilter,
    goBack
  } = useModel('parent');

  // 获取学校列表
  const schoolOptions = useMemo(() => {
    const schools = new Map();
    studentList.forEach(student => {
      if (!schools.has(student.school_code)) {
        schools.set(student.school_code, {
          code: student.school_code,
          name: `学校 ${student.school_code}`, // 这里可以从studentQuestionnaireStatus获取真实学校名称
        });
      }
    });

    // 从问卷状态中获取真实学校名称
    studentQuestionnaireStatus.forEach(status => {
      if (schools.has(status.school_code)) {
        schools.set(status.school_code, {
          code: status.school_code,
          name: status.school_name,
        });
      }
    });

    return Array.from(schools.values());
  }, [studentList, studentQuestionnaireStatus]);

  // 筛选后的学生列表
  const filteredStudentList = useMemo(() => {
    if (!schoolCodeFilter) return studentList;
    return studentList.filter(student => student.school_code === schoolCodeFilter);
  }, [studentList, schoolCodeFilter]);

  // 处理学生选择
  const handleSelectStudent = async (student: API.ISSoStudentInfo) => {
    await selectStudent(student);
  };

  // 处理学校筛选
  const handleSchoolFilterChange = (schoolCode: string) => {
    setSchoolFilter(schoolCode);
  };

  // 获取年级颜色
  const getGradeColor = (grade: string) => {
    const gradeColors: Record<string, string> = {
      一年级: 'red',
      二年级: 'orange',
      三年级: 'gold',
      四年级: 'green',
      五年级: 'blue',
      六年级: 'purple',
    };
    return gradeColors[grade] || 'default';
  };

  return (
    <div className="student-select-container">
      <div className="student-select-content">
        <Card className="select-card">
          <div className="card-header">
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={goBack}
              className="back-button"
            >
              返回
            </Button>

            <div className="header-info">
              <Title level={3} className="header-title">
                选择学生
              </Title>
              <Text type="secondary" className="header-subtitle">
                {parentInfo?.name && `${parentInfo.name}，`}请选择要评价的学生
              </Text>
            </div>

            {/* 学校筛选 */}
            {schoolOptions.length > 1 && (
              <div className="school-filter">
                <Space>
                  <FilterOutlined />
                  <Text>学校筛选：</Text>
                  <Select
                    style={{ width: 200 }}
                    placeholder="选择学校"
                    allowClear
                    value={schoolCodeFilter || undefined}
                    onChange={handleSchoolFilterChange}
                    options={[
                      { label: '全部学校', value: '' },
                      ...schoolOptions.map(school => ({
                        label: school.name,
                        value: school.code,
                      })),
                    ]}
                  />
                </Space>
              </div>
            )}
          </div>

          {/* 标签页 */}
          <Tabs
            defaultActiveKey="list"
            items={[
              {
                key: 'list',
                label: '学生列表',
                children: (
                  <div>
                    <div className="student-list">
                      {filteredStudentList.length === 0 ? (
                        <Alert
                          message="暂无关联学生"
                          description={
                            schoolCodeFilter
                              ? "该学校暂无关联学生信息"
                              : "您的手机号暂未关联任何学生信息，请联系学校确认"
                          }
                          type="warning"
                          showIcon
                        />
                      ) : (
                        <Space
                          direction="vertical"
                          size="large"
                          style={{ width: '100%' }}
                        >
                          {filteredStudentList.map((student) => (
                            <Card
                              key={student.id}
                              className="student-card"
                              hoverable
                              onClick={() => handleSelectStudent(student)}
                            >
                              <div className="student-info">
                                <div className="student-avatar">
                                  <Avatar size={64} icon={<UserOutlined />} />
                                </div>

                                <div className="student-details">
                                  <div className="student-name">
                                    <Title level={4}>{student.name}</Title>
                                    <Tag color={getGradeColor(student.grade)}>
                                      {student.grade}
                                    </Tag>
                                  </div>

                                  <div className="student-class">
                                    <Text type="secondary">班级：{student.class}</Text>
                                  </div>

                                  <div className="student-status">
                                    <Tag
                                      color={
                                        student.status === 'active'
                                          ? 'success'
                                          : 'default'
                                      }
                                    >
                                      {student.status === 'active' ? '在读' : '其他'}
                                    </Tag>
                                  </div>
                                </div>

                                <div className="student-action">
                                  <Button
                                    type="primary"
                                    icon={<ArrowRightOutlined />}
                                    loading={loading}
                                    size="large"
                                  >
                                    选择评价
                                  </Button>
                                </div>
                              </div>
                            </Card>
                          ))}
                        </Space>
                      )}
                    </div>

                    <div className="select-tips">
                      <Alert
                        message="评价说明"
                        description={
                          <div>
                            <p>• 选择学生后将进入问卷评价页面</p>
                            <p>• 每个学生每月只能评价一次</p>
                            <p>• 评价内容包括学校整体评价和各科教师评价</p>
                          </div>
                        }
                        type="info"
                        showIcon
                      />
                    </div>
                  </div>
                ),
              },
              {
                key: 'status',
                label: '问卷状态',
                children: <StudentQuestionnaireStatus />,
              },
            ]}
          />
        </Card>
      </div>
    </div>
  );
};

export default StudentSelect;
