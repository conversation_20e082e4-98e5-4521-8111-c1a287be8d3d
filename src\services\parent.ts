// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 家长端服务
 * @description 家长端问卷填写相关的API接口
 */

/** 验证家长手机号 POST /api/parent/verify-phone */
export async function verifyParentPhone(params: API.IParentPhoneVerifyParams) {
  return request<API.ResType<API.IParentPhoneVerifyResponse>>(
    '/api/parent/verify-phone',
    {
      method: 'POST',
      data: params,
    },
  );
}

/** 获取学生的教师列表 GET /api/parent/student-teachers */
export async function getStudentTeachers(
  params: API.IGetStudentTeachersParams,
) {
  return request<API.ResType<API.IStudentTeacherInfo[]>>(
    '/api/parent/student-teachers',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取学生的问卷信息 GET /api/parent/questionnaire */
export async function getQuestionnaireForParent(
  params: API.IGetQuestionnaireForParentParams,
) {
  return request<API.ResType<API.IParentQuestionnaireInfo>>(
    '/api/parent/questionnaire',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取学生的所有问卷列表 GET /api/parent/student-questionnaires */
export async function getStudentQuestionnaires(
  params: API.IGetStudentQuestionnairesParams,
) {
  return request<API.ResType<API.IStudentQuestionnairesResponse>>(
    '/api/parent/student-questionnaires',
    {
      method: 'GET',
      params,
    },
  );
}

/** 提交家长端问卷评价 POST /api/parent/submit-evaluation */
export async function submitParentEvaluation(
  params: API.IParentSubmitEvaluationParams,
) {
  return request<API.ResType<API.IParentSubmitEvaluationResponse>>(
    '/api/parent/submit-evaluation',
    {
      method: 'POST',
      data: params,
    },
  );
}

/** 检查学生是否已提交评价 GET /api/parent/check-submission */
export async function checkStudentSubmission(params: {
  student_id: string;
  questionnaire_id: number;
  parent_phone: string;
}) {
  return request<API.ResType<{ is_submitted: boolean; submitted_at?: Date }>>(
    '/api/parent/check-submission',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取学生问卷状态（批量查询） GET /api/parent/students-questionnaire-status */
export async function getStudentsQuestionnaireStatus(params: {
  student_ids: string[];
  parent_phone: string;
  school_code?: string;
}) {
  return request<API.ResType<API.IStudentQuestionnaireStatus[]>>(
    '/api/parent/students-questionnaire-status',
    {
      method: 'GET',
      params: {
        ...params,
        student_ids: params.student_ids.join(','),
      },
    },
  );
}
